declare const browser: {
    runtime: {
        getURL(path: string): string;
    };
};

/**
 * Waits for DOMContentLoaded (if not already fired) and then
 * watches for any DOM mutations. Once there have been no
 * mutations for `stableTime` milliseconds, resolves the Promise.
 *
 * @param {number} stableTime - milliseconds of stability to wait for (default 1000)
 * @param target
 * @returns {Promise<void>}
 */
function waitForDomStable(stableTime: number = 1000, target: Node = document): Promise<void> {
    return new Promise((resolve) => {
        // Step 1: Wait for initial DOMContentLoaded if target is document
        const whenLoaded = (target === document && document.readyState === 'loading')
            ? new Promise(r => document.addEventListener('DOMContentLoaded', r, {once: true}))
            : Promise.resolve();

        whenLoaded.then(() => {
            let timerId = null;
            const resetTimer = () => {
                if (timerId !== null) clearTimeout(timerId);
                timerId = setTimeout(() => {
                    observer.disconnect();
                    resolve(void 0);
                }, stableTime);
            };

            const observer = new MutationObserver(() => {
                resetTimer();
            });

            observer.observe(target, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });

            resetTimer();
        });
    });
}

/**
 * Waits until the given class is present on the target element.
 * Resolves immediately if the class is already present.
 */
function waitForClass(target: Element, className: string): Promise<void> {
    return new Promise((resolve) => {
        // Defensive: only resolve if className is non-empty and target is valid
        if (!className || !target) return;

        const hasClass = () => target.classList.contains(className);

        if (hasClass()) {
            resolve();
            return;
        }
        const observer = new MutationObserver(() => {
            if (hasClass()) {
                console.log(target);
                alert(hasClass());
                observer.disconnect();
                resolve();
            }
        });
        observer.observe(target, {attributes: true, attributeFilter: ['class']});
    });
}

class PlayerBoard {
    lines: TilePlacement[] = [];

    constructor() {
        while (this.lines.length < 5) {
            this.lines.push(null);
        }
    }

    addTilesToLine(lineNumber: number, tilePlacement: TilePlacement): TilePlacement | null {
        // lineNumber is 1-based, so index is lineNumber - 1
        const idx = lineNumber - 1;
        const maxTiles = lineNumber;

        const existing = this.lines[idx];

        if (existing && existing.tileType === tilePlacement.tileType) {
            existing.count += tilePlacement.count;
            if (existing.count > maxTiles) {
                const excess = existing.count - maxTiles;
                existing.count = maxTiles;
                return new TilePlacement(excess, tilePlacement.tileType);
            }
            return null;
        } else if (!existing) {
            // No tile yet on this line
            if (tilePlacement.count > maxTiles) {
                this.lines[idx] = new TilePlacement(maxTiles, tilePlacement.tileType);
                return new TilePlacement(tilePlacement.count - maxTiles, tilePlacement.tileType);
            } else {
                this.lines[idx] = new TilePlacement(tilePlacement.count, tilePlacement.tileType);
                return null;
            }
        } else {
            // There is a different tile type already on this line, can't add
            // (You may want to throw or handle this case differently)
            return tilePlacement; // All tiles are excess
        }
    }
}

class Tile {
    count: number;
    element: HTMLDivElement;

    constructor(initialCount: number, type: number) {
        const mappedType = ((type + 1) % 5) + 1;
        const className = 'tile tile' + mappedType.toString();

        this.count = initialCount;
        this.element = document.createElement('div');
        this.element.className = className;
        this.element.innerText = this.count.toString();
    }

    setCount(newCount: number) {
        this.count = newCount;
        this.element.innerText = this.count.toString();
    }
}

class TilePlacement {
    count: number;
    tileType: number;

    constructor(count: number, tileType: number) {
        this.count = count;
        this.tileType = tileType;
    }
}

class Bag {
    private tiles: TilePlacement[] = [];

    addTile(tilePlacement: TilePlacement): void {
        const index = (tilePlacement.tileType + 2) % 5;
        if (this.tiles[index] === undefined || this.tiles[index] === null) {
            this.tiles[index] = new TilePlacement(tilePlacement.count, tilePlacement.tileType);
        } else {
            this.tiles[index].count += tilePlacement.count;
        }
        console.log('discard: ' + index + ' ; count: ' + tilePlacement.count);
    }

    removeTile(tilePlacement: TilePlacement): void {
        const index = (tilePlacement.tileType + 2) % 5;
        if (this.tiles[index] && this.tiles[index].count >= tilePlacement.count) {
            console.log('undiscard: ' + index + ' ; count: ' + tilePlacement.count);
            this.tiles[index].count -= tilePlacement.count;
            if (this.tiles[index].count === 0) {
                this.tiles[index] = null;
            }
        }
    }

    getTiles(): TilePlacement[] {
        return this.tiles;
    }

    clear(): void {
        this.tiles = [];
    }
}

class Game {
    private static readonly factoriesByPlayers: { [key: number]: number } = {
        2: 5,
        3: 7,
        4: 9
    };

    private logChangeQueue: Element[] = [];
    private isProcessingInitialLog = false;

    availableTiles: Tile[] = [];
    stateLoaded = false;
    logsError = false;
    tilesInBagCount: number;
    boardBag: Bag;
    discardedBag: Bag;
    playersBoards: { [playerName: string]: PlayerBoard } = {};
    lastMoves: {
        [playerName: string]: {
            lineNumber: number,
            tilePlacement: TilePlacement,
            discardedTiles?: TilePlacement
        }
    } = {};
    factoriesCount: number;

    isSupported(): boolean {
        const ogUrl = document.querySelector('meta[property="og:url"]');

        return !ogUrl || !ogUrl.getAttribute('content').includes('/azul/');
    }

    init() {
        this.initializeAddOnStructure();

        // Wait until both containers exist, then wait for stability
        const checkContainers = () => {
            const logs = document.getElementById('logs');
            const factories = document.getElementById('factories');
            if (logs && factories) {
                this.initializeTranslations();
                this.initializeGame();
                this.initializeLogObserve();
                this.waitForGameStable();
            } else {
                setTimeout(checkContainers, 100);
            }
        };
        checkContainers();
    }

    private async initializeTranslations(): Promise<void> {
        await this.waitForI18nConfig();
        // wait for bgaConfig to be available
        const i18n = this.getI18n();

        if (!i18n) {
            console.warn('i18n object not available');
            return;
        }

        for (const key in I18nTranslations.operationTypeByKey) {
            if (typeof i18n.nlsStrings[i18n.activeBundle][key] !== 'undefined') {
                I18nTranslations.addTranslation(key, i18n.nlsStrings[i18n.activeBundle][key]);
            } else if (typeof i18n.nlsStrings['lang_mainsite-' + i18n.jsbundlesversion][key] !== 'undefined') {
                I18nTranslations.addTranslation(key, i18n.nlsStrings['lang_mainsite-' + i18n.jsbundlesversion][key]);
            }
        }
    }

    // https://pl.boardgamearena.com/10/azul?table=707354797
    private async loadLog(fromFile: boolean = false): Promise<void> {
        this.logsError = false;
        let data: string;
        try {
            if (fromFile === false) {
                // wait for bgaConfig to be available
                await this.waitForBgaConfig();

                // Get the current page URL to extract table ID and other parameters
                const currentUrl = new URL(window.location.href);
                const tableId = currentUrl.searchParams.get('table');
                if (!tableId) {
                    console.warn('No table ID found in URL');
                    return;
                }

                // extract server ID from meta property
                const ogUrlMeta = document.querySelector('meta[property="og:url"]');
                let serverId = 0;

                if (ogUrlMeta) {
                    const ogUrl = ogUrlMeta.getAttribute('content');
                    if (ogUrl) {
                        const match = ogUrl.match(/boardgamearena\.com\/(\d+)\//);
                        if (match && match[1]) {
                            serverId = parseInt(match[1], 10);
                        }
                    }
                }

                if (serverId === 0) {
                    console.warn('No server ID found in URL');
                    return;
                }

                // extract headers from the current page's requests
                const headers = await this.getRequestHeaders();

                const timestamp = Date.now();
                const notificationUrl = `https://en.boardgamearena.com/${serverId}/azul/azul/notificationHistory.html?table=${tableId}&from=1&privateinc=1&history=0&noerrortracking=true&dojo.preventCache=${timestamp}`;


                /*const notificationUrl = `https://boardgamearena.com/table/table/notificationHistory.html?table=${tableId}&from=1&privateinc=1&history=1&noerrortracking=true&dojo.preventCache=${timestamp}`;*/

                console.log(notificationUrl);
                console.log(headers);
                const response = await fetch(notificationUrl, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Include cookies automatically
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                data = await response.text();
            } else {
                const jsonUrl = browser.runtime.getURL('2_players.json');
                data = await fetch(jsonUrl).then(res => res.text());
            }

            /*const delay = millis => new Promise((resolve, reject) => {
                setTimeout(_ => resolve(), millis)
            });

            await delay(10000);*/


            console.log('Notification history loaded:', data);
            alert('gggg');
            // Parse and process the notification history data
            this.processNotificationHistory(data);

        } catch (error) {
            console.error('Error loading log:', error);
            this.logsError = true;
        }
    }

    private async getRequestHeaders(): Promise<Headers> {
        const headers = new Headers();

        // Set basic headers
        headers.set('User-Agent', 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:131.0) Gecko/20100101 Firefox/131.0');
        headers.set('Accept', '*/*');
        headers.set('Accept-Language', 'en-US,en;q=0.5');
        headers.set('Accept-Encoding', 'gzip, deflate, br, zstd');
        headers.set('X-Requested-With', 'XMLHttpRequest');
        headers.set('Connection', 'keep-alive');
        headers.set('Referer', window.location.href);
        headers.set('Sec-Fetch-Dest', 'empty');
        headers.set('Sec-Fetch-Mode', 'cors');
        headers.set('Sec-Fetch-Site', 'same-origin');
        headers.set('TE', 'trailers');

        // Try to get X-Request-Token from existing requests or meta tags
        const requestToken = this.getRequestToken();
        alert(requestToken);
        if (requestToken) {
            headers.set('X-Request-Token', requestToken);
        }

        return headers;
    }

    private getRequestToken(): string | null {
        // try to access bgaConfig through wrappedJSObject
        if ((window as any).wrappedJSObject && (window as any).wrappedJSObject.bgaConfig) {
            return (window as any).wrappedJSObject.bgaConfig.requestToken;
        }

        console.warn('Could not find X-Request-Token');

        return null;
    }

    private async waitForI18nConfig(): Promise<void> {
        return new Promise((resolve) => {
            const checkI18n = () => {
                if (typeof (window as any).wrappedJSObject !== 'undefined') {
                    const unsafeWindow = (window as any).wrappedJSObject;
                    if (typeof unsafeWindow.g_i18n === 'object') {
                        resolve();
                        return;
                    }
                }
                setTimeout(checkI18n, 100);
            };
            checkI18n();
        });
    }

    private getI18n(): I18nObject | null {
        // try to access g_i18n through wrappedJSObject
        if ((window as any).wrappedJSObject && (window as any).wrappedJSObject.g_i18n) {
            return (window as any).wrappedJSObject.g_i18n as I18nObject;
        }

        console.warn('Could not find g_i18n object');

        return null;
    }

    private async waitForBgaConfig(): Promise<void> {
        return new Promise((resolve) => {
            const checkBgaConfig = () => {
                if (typeof (window as any).wrappedJSObject !== 'undefined') {
                    const unsafeWindow = (window as any).wrappedJSObject;
                    if (unsafeWindow.bgaConfig && unsafeWindow.bgaConfig.requestToken) {
                        resolve();
                        return;
                    }
                }
                setTimeout(checkBgaConfig, 100);
            };
            checkBgaConfig();
        });
    }

    private processNotificationHistory(data: string): void {
        const doc = JSON.parse(data);

        console.log(doc.data.data);
        let i = 0;
        for (const packet of doc.data.data) {
            i++;
            console.log(packet);

            for (const packetEvent of packet.data) {
                console.log(packetEvent);

                if (packetEvent.type === 'factoriesFilled') {
                    console.log('DEBUG: factories', packetEvent.args.factories);
                    this.factoriesCount = packetEvent.args.factories.length - 1;

                    const totalDiscarded = this.newRoundBegins();

                    this.tilesInBagCount = packetEvent.args.remainingTiles;

                    for (const factory of packetEvent.args.factories) {
                        for (const factoryTile of factory) {
                            if (factoryTile.type === 0) {
                                continue;
                            }
                            this.reduceAvailableTiles(new TilePlacement(1, factoryTile.type));
                        }
                    }

                    console.log('DEBUG: available tiles', this.availableTiles);


                } else if (packetEvent.type === 'undoSelectLine') {
                    this.undoSelectLine(packetEvent.args.player_name);

                    console.log('DEBUG: players boards', this.playersBoards);


                } else if (packetEvent.type === 'tilesPlacedOnLine') {
                    this.placeTileOnLine(packetEvent.args.player_name, packetEvent.args.lineNumber, packetEvent.args.number, packetEvent.args.type);

                    console.log('DEBUG: players boards', this.playersBoards);
                    console.log('DEBUG: discarded bag ', this.discardedBag)

                } else if (packetEvent.type === 'tilesSelected') {
                    console.log('DEBUG: tiles selected from factory ' + packetEvent.args.fromFactory + ' color ' + packetEvent.args.color);
                }

            }
        }

    }

    private initializeGame(): void {
        this.discardedBag = new Bag();
        this.boardBag = new Bag();
        this.availableTiles = [];

        for (let i = 1; i <= 5; i++) {
            this.availableTiles.push(new Tile(20, i));
        }
        this.tilesInBagCount = 100;

        const tileContainer = document.getElementById('azul_tile_counter');
        for (const tile of this.availableTiles) {
            tileContainer.appendChild(tile.element);
        }

        const playersContainer = document.getElementById('player_boards');
        if (playersContainer) {
            const playerBoards = playersContainer.querySelectorAll('.player-board');
            playerBoards.forEach(board => {
                const playerNameElem = board.querySelector('.player-name a');
                if (playerNameElem) {
                    const playerName = playerNameElem.textContent.trim();
                    this.playersBoards[playerName] = new PlayerBoard();
                }
            });
        }

        // Set the number of factories based on the number of player boards
        const numPlayers = Object.keys(this.playersBoards).length;
        this.factoriesCount = Game.factoriesByPlayers[numPlayers] ?? 0;

        this.tilesInBagCount -= this.factoriesCount * 4;

        console.log(this.playersBoards);
    }

    /**
     * Helper to extract tile placements from a container.
     * Groups tiles by type and returns an array of TilePlacement.
     */
    private extractTilePlacements(container: Element): TilePlacement[] {
        console.log(container);
        const tileDivs = container.querySelectorAll('div.tile');
        console.log(tileDivs);
        const typeCountMap = new Map<number, number>();

        tileDivs.forEach(tileDiv => {
            const match = Array.from(tileDiv.classList).find(cls => cls.startsWith('tile') && cls.length > 4);
            if (match) {
                const tileType = parseInt(match.replace('tile', ''), 10);
                // first player tile
                if (tileType === 0) {
                    return;
                }
                typeCountMap.set(tileType, (typeCountMap.get(tileType) || 0) + 1);
            }
        });

        return Array.from(typeCountMap.entries()).map(
            ([tileType, count]) => new TilePlacement(count, tileType)
        );
    }

    private reduceAvailableTilesFromBoard() {
        const container = document.getElementById('factories');
        if (!container) {
            return null;
        }

        const tilePlacements = this.extractTilePlacements(container);
        tilePlacements.forEach(tp => this.reduceAvailableTiles(tp));
    }

    private initializeLogObserve() {
        const logContainer = document.getElementById('logs');

        if (logContainer) {
            // Set flag to indicate we're processing initial log
            this.isProcessingInitialLog = true;
            // Start observing for changes immediately
            this.observeForLogEntry(logContainer);

            // Load existing log history and wait for DOM to be stable
            Promise.all([
                this.loadLog(false),
                waitForDomStable(200, logContainer)
            ]).then(() => {
                // Now process any changes that were queued during initial log processing
                this.processQueuedLogChanges();

                // Mark that initial processing is complete
                this.isProcessingInitialLog = false;
            });
        } else {
            // it should not happen because we wait for logs to be ready
            alert('hhhh');
            const observer = new MutationObserver(() => {
                const logsWrapNow = document.getElementById('logs');
                if (logsWrapNow) {
                    observer.disconnect();
                    this.observeForLogEntry(logsWrapNow);
                }
            });
            observer.observe(document.body, {childList: true, subtree: true});
        }
    }

    private processQueuedLogChanges() {
        console.log('DEBUG: Process queue log changes', this.logChangeQueue);

        let ignoreMoves = true;
        while (this.logChangeQueue.length > 0) {
            const element = this.logChangeQueue.shift();

            if (element.textContent && I18nTranslations.matches(element.textContent, 'Replay last moves') !== false) {
                ignoreMoves = false;
                continue;
            }

            if (element && !ignoreMoves) {
                console.log('DEBUG: analyze queue');
                this.analyzeLog(element);
            }
        }
    }

    private initializeAddOnStructure() {
        const archiveControl = document.getElementById('current_header_infos_wrap');
        if (!archiveControl) {
            console.warn('archivecontrol not found');
            return;
        }

        // Create the container for tile counts
        const tileContainer = document.createElement('div');
        tileContainer.id = 'azul_tile_counter';
        tileContainer.className = 'azul-tile-counter';

        archiveControl.appendChild(tileContainer);
    }

    observeForLogEntry(target: HTMLElement) {
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;

                        // Check if this is a log entry (roundedbox)
                        if (element.classList.contains('roundedbox')) {
                            if (this.isProcessingInitialLog) {
                                // Queue the change for later processing
                                this.logChangeQueue.push(element);
                            } else {
                                // Process immediately in real-time
                                this.analyzeLog(element);
                            }
                        }

                        // Also check for nested log entries
                        const nestedLogs = element.querySelectorAll('.roundedbox');
                        nestedLogs.forEach(logElement => {
                            if (this.isProcessingInitialLog) {
                                // Queue the change for later processing
                                this.logChangeQueue.push(logElement);
                            } else {
                                // Process immediately in real-time
                                this.analyzeLog(logElement);
                            }
                        });
                    }
                }
            }
        });

        observer.observe(target, {childList: true, subtree: true});
    }


    private reduceAvailableTiles(tilePlacement: TilePlacement) {
        const index = (tilePlacement.tileType + 2) % 5;
        const tile = this.availableTiles[index];
        if (tile) {
            tile.setCount(tile.count - tilePlacement.count);
        } else {
            console.warn('Tile not found for type:', tilePlacement.tileType);
        }
    }

    private newRoundBegins(): number {
        // iterate over all player boards
        for (const playerName in this.playersBoards) {
            const board = this.playersBoards[playerName];
            for (let i = 0; i < board.lines.length; i++) {
                const line = board.lines[i];
                const lineNumber = i + 1;
                if (line && line.count === lineNumber) {
                    // Add 1 tile to boardBag
                    this.boardBag.addTile(new TilePlacement(1, line.tileType));
                    // Add remaining tiles to discardedBag (if any)
                    if (lineNumber > 1) {
                        this.discardedBag.addTile(new TilePlacement(lineNumber - 1, line.tileType));
                        console.log('discarded bag');
                        console.log(this.discardedBag);
                    }
                    // Clear the line
                    board.lines[i] = null;
                }
            }
        }

        // check if we need to refill from discarded bag
        if (this.tilesInBagCount - this.factoriesCount * 4 < 0) {
            // get all tiles from discarded bag
            const discardedTiles = this.discardedBag.getTiles();

            // add them back to available tiles
            discardedTiles.forEach(tilePlacement => {
                if (tilePlacement) {
                    const index = (tilePlacement.tileType + 2) % 5;
                    const tile = this.availableTiles[index];
                    if (tile) {
                        tile.setCount(tile.count + tilePlacement.count);
                    }
                }
            });

            // increase tiles in bag by the number of discarded tiles
            const totalDiscarded = discardedTiles.reduce((sum, tp) => sum + (tp ? tp.count : 0), 0);

            // clear the discarded bag
            this.discardedBag.clear();

            return totalDiscarded;
        }

        return 0;
    }

    private placeTileOnLine(playerName: string, lineNumber: number, count: number, tileType: number) {
        // ignores first player tile for now
        if (tileType === 0) {
            return;
        }
        const tilePlacement = new TilePlacement(count, tileType);
        if (lineNumber > 0) {
            const discardedTiles = this.playersBoards[playerName].addTilesToLine(lineNumber, tilePlacement);
            this.lastMoves[playerName] = {lineNumber, tilePlacement: tilePlacement, discardedTiles};

            if (discardedTiles !== null) {
                this.discardedBag.addTile(discardedTiles);
            }

            return;
        }

        this.discardedBag.addTile(tilePlacement);

        if (playerName) {
            this.lastMoves[playerName] = {
                lineNumber: null,
                tilePlacement: tilePlacement,
                discardedTiles: tilePlacement
            };
        }
    }

    private undoSelectLine(playerName: string): void {
        if (this.lastMoves[playerName]) {
            const {lineNumber, tilePlacement, discardedTiles} = this.lastMoves[playerName];
            if (lineNumber !== null) {
                // remove the tiles from the line (reverse addTilesToLine)
                const board = this.playersBoards[playerName];
                const idx = lineNumber - 1;
                const line = board.lines[idx];
                if (line && line.tileType === tilePlacement.tileType) {
                    line.count -= tilePlacement.count;
                    if (line.count <= 0) {
                        board.lines[idx] = null;
                    }
                }
                // remove from discardedBag if it was added before
                if (discardedTiles) {
                    this.discardedBag.removeTile(discardedTiles);
                    console.log('undiscarded bag');
                }
            } else {
                // discard-only move: just remove from discardedBag
                if (discardedTiles) {
                    this.discardedBag.removeTile(discardedTiles);
                    console.log('undiscarded bag');
                }
            }

            // remove the last move record
            delete this.lastMoves[playerName];
        }
    }

    /**
     * Analyzes the content of logs_wrap to find tile placement actions.
     * @param container The HTMLElement to search within (e.g., logs_wrap)
     * @returns null if no "umieszcza" found, or an object with playerName, tilePlacements, and lineNumber
     */
    private analyzeLog(container: Element): {
        playerName: string,
        tilePlacements: TilePlacement[],
        lineNumber: number
    } | null {
        console.log(container.textContent);

        const parsed = this.parseLogEntry(container.textContent);

        if (parsed === null) {
            return null;
        }

        if (parsed.type === 'tile_placed_on_line') {
            const playerName = parsed.player_name ?? null;
            const tilePlacements = this.extractTilePlacements(container);
            const lineNumber = parsed.lineNumber ?? null;

            if (playerName && tilePlacements.length > 0 && lineNumber !== null) {
                this.placeTileOnLine(playerName, lineNumber, tilePlacements[0].count, tilePlacements[0].tileType);
            } else if (lineNumber === null) {
                console.log('should not be here, because tile placed on floor is another operation');
                this.placeTileOnLine(playerName, 0, tilePlacements[0].count, tilePlacements[0].tileType);
            }

            console.log(this.playersBoards[playerName].lines);

            return null;
        }

        if (parsed.type === 'tile_placed_on_floor') {
            const playerName = parsed.player_name ?? null;
            const tilePlacements = this.extractTilePlacements(container);

            this.placeTileOnLine(playerName, 0, tilePlacements[0].count, tilePlacements[0].tileType);

            return null;
        }


        if (parsed.type === 'new_round_starts') {
            const totalDiscarded = this.newRoundBegins();

            // decrease tiles in bag by factories × 4 + add tiles count from discarded bag if needed
            this.tilesInBagCount -= this.factoriesCount * 4 + totalDiscarded;
            if (this.stateLoaded === true) {
                const logs = document.getElementById('logs');
                const factories = document.getElementById('centered-table');
                Promise.all([
                    waitForDomStable(200, logs),
                    waitForDomStable(1000, factories),
                ]).then(() => {
                    this.reduceAvailableTilesFromBoard();
                });
            }

            return null;
        }

        if (parsed.type === 'cancels_tile_placement') {
            const playerName = parsed.player_name ?? null;

            if (playerName) {
                this.undoSelectLine(playerName);
            }

            console.log(this.playersBoards);
            return null;
        }

        /*
        if (container.textContent && container.textContent.includes(I18nTranslations.get('new_round_starts'))) {
            waitForDomStable(200).then(() => {
                this.reduceAvailableTilesFromBoard();
            });
        }*/

        return null;
    }

    private parseLogEntry(text: string) {
        for (let operationText in I18nTranslations.operationTypeByKey) {
            const parsed = I18nTranslations.matches(text, operationText);
            if (parsed !== false) {
                return parsed;
            }
        }

        return null;
    }

    waitForGameStable() {
        const logs = document.getElementById('logs');
        const factories = document.getElementById('centered-table');
        if (!logs || !factories) {
            console.warn('Logs or factories container not found!');
            return;
        }

        Promise.all([
            waitForDomStable(200, logs),
            waitForDomStable(1000, factories),
        ]).then(() => {
            this.onGameStable();
        });
    }

    onGameStable() {
        console.log('Game is now stable!');
        if (this.logsError) {
            this.reduceAvailableTilesFromBoard();
        }
        //this.boardBag.getTiles().forEach(tp => this.reduceAvailableTiles(tp));
        //this.discardedBag.getTiles().forEach(tp => this.reduceAvailableTiles(tp));

        /*for (const playerName in this.playersBoards) {
            const board = this.playersBoards[playerName];
            board.lines.forEach(tp => {
                if (tp !== null) {
                    this.reduceAvailableTiles(tp);
                }
            });
        }*/

        this.stateLoaded = true;
    }
}

// https://boardgamearena.com/translation?module_id=1467&source_locale=en_US&dest_locale=pl_PL&findtype=all&find=places
class I18nTranslations {
    public static translations: Record<string, string> = {};

    // Map canonical keys -> operation types (language independent)
    public static operationTypeByKey: Record<string, string> = {
        '${player_name} places ${number} ${color} on line ${lineNumber}': 'tile_placed_on_line',
        '${player_name} cancels tile placement': 'cancels_tile_placement',
        '${player_name} places ${number} ${color} on floor line': 'tile_placed_on_floor_line',
        'A new round begins !': 'new_round_starts',
        'Replay last moves': 'replay_last_moves',
    };

    private static defaultLanguage = 'en';

    // cache for getMatcher results
    private static matcherCache: Record<string, RegExp | string> = {};

    static addTranslation(key: string, translation: string): void
    {
        this.translations[key] = translation;
    }

    /** @deprecated */
    static getLanguage(): string {
        // Read lang attribute and normalize to short code (en, pl, ...)
        const htmlElement = document.documentElement;
        const rawLang = htmlElement.getAttribute('lang') ||
            htmlElement.getAttribute('xml:lang') ||
            htmlElement.getAttribute('xmlns:lang') ||
            (navigator.language || '');
        const langCode = rawLang ? rawLang.split(/[-_]/)[0] : this.defaultLanguage;
        return this.translations[langCode] ? langCode : this.defaultLanguage;
    }

    static get(key: string): string {
        return this.translations[key] || key;
    }

    /**
     * Check if a key contains placeholders that require regex matching
     */
    static hasPlaceholders(key: string): boolean {
        return /\$\{[^}]+\}/.test(key);
    }

    /**
     * Get the appropriate matcher for a key - either regex or direct string
     */
    static getMatcher(key: string): RegExp | string {
        if (this.matcherCache[key]) {
            return this.matcherCache[key];
        }

        let result: RegExp | string;
        if (this.hasPlaceholders(key)) {
            result = this.getRegex(key);
        } else {
            result = this.get(key);
        }

        this.matcherCache[key] = result;

        return result;
    }

    /**
     * Test if text matches a key, automatically choosing between regex and direct comparison
     * Returns parsed match object for regex matches or direct string matches, false if not found
     */
    static matches(text: string, key: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } | false {
        const matcher = this.getMatcher(key);

        if (matcher instanceof RegExp) {
            // Remove HTML comments from the text for regex matching
            const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');
            const match = cleanText.match(matcher);

            if (match) {
                const parsedMatch = this.parseMatch(match, key);
                return parsedMatch || false;
            } else {
                return false;
            }
        } else {
            // direct string comparison for keys without placeholders
            if (!text.includes(matcher)) {
                return false;
            }

            return {type: this.operationTypeByKey[matcher]}
        }
    }

    /**
     * Build a regex for the given canonical key using the current language's translation.
     * Replaces placeholders with HTML-aware capture groups:
     *  - ${player_name} -> an element with class "playername"
     *  - ${number} -> one or more tile divs (we capture the sequence)
     *  - ${color} -> either a tile class (e.g. tile3) or a plain word
     *  - ${lineNumber} -> a <strong>digit</strong> or plain digit
     */
    static getRegex(key: string): RegExp {
        const template = this.translations[key] || key;

        // Remove HTML comments from the template
        let tmp = template.replace(/<!--[\s\S]*?-->/g, '');

        // Replace placeholders with markers
        tmp = tmp
            .replace(/\$\{player_name\}/g, '___PLAYER_NAME___')
            .replace(/\$\{number\}/g, '___NUMBER___')
            .replace(/\$\{color\}/g, '') // remove color
            .replace(/\$\{lineNumber\}/g, '___LINENUM___');

        // Trim extra spaces and normalize whitespace
        tmp = tmp.trim().replace(/\s+/g, ' ');

        // Escape regex-special characters
        tmp = tmp.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Replace markers with HTML-aware capture groups
        tmp = tmp.replace(
            /___PLAYER_NAME___/g,
            '(<[^>]*class=["\']playername["\'][^>]*>[\\s\\S]*?<\\/[^>]*>)'
        );

        tmp = tmp.replace(
            /___NUMBER___/g,
            '((?:<div[^>]*class=["\'][^"\']*tile(\\d+)[^"\']*["\'][^>]*>\\s*<\\/div>\\s*)+)'
        );

        tmp = tmp.replace(
            /___LINENUM___/g,
            '(?:<strong>\\s*([0-9]+)\\s*<\\/strong>|([0-9]+))'
        );

        // Clean up extra spaces and make the pattern more flexible
        tmp = tmp.replace(/\\s{2,}/g, '\\s*');

        // Allow arbitrary surrounding content
        return new RegExp('[\\s\\S]*' + tmp + '[\\s\\S]*', 'i');
    }

    /**
     * Try to find the canonical key whose translation matches the provided text.
     * Returns the key or null.
     */
    static findMatchingKey(text: string): string | null {
        // remove HTML comments from the text for matching
        const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');

        for (const key in this.translations) {
            const regex = this.getRegex(key);
            if (regex.test(cleanText)) {
                return key;
            }
        }

        return null;
    }

    static has(key: string): boolean {
        return key in this.translations;
    }

    /**
     * Parse the RegExp match into a structured object.
     * Note: this relies on the same placeholder order that getRegex uses.
     */
    static parseMatch(match: RegExpMatchArray, key: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } {
        // Find placeholder order from the canonical key's translation (placeholders in that string)
        const placeholders: string[] = [];
        const placeholderRegex = /\$\{([^}]+)\}/g;
        let placeholderMatch: string[];
        while ((placeholderMatch = placeholderRegex.exec(key)) !== null) {
            placeholders.push(placeholderMatch[1]);
        }

        const result: any = {};

        // Operation type is mapped from the canonical key (language-independent)
        result.type = this.operationTypeByKey[key] || 'unknown';

        // The getRegex created multiple capture groups for some placeholders; we need to step through match groups.
        // We'll iterate through placeholders and attempt to locate the correct capture groups manually.
        // Because some placeholders expand into multiple capturing groups (color, lineNumber), we carefully attempt to extract.
        let groupIndex = 1; // match[0] is full match

        for (const placeholder of placeholders) {
            if (groupIndex >= match.length) break;

            switch (placeholder) {
                case 'player_name': {
                    const playerHtml = match[groupIndex] || '';
                    // extract inner text of .playername element
                    const playerNameMatch = playerHtml.match(/<[^>]*class=["']playername["'][^>]*>([^<]*)</i);
                    result.player_name = playerNameMatch ? playerNameMatch[1].trim() : playerHtml;
                    groupIndex += 1;
                    break;
                }
                case 'number': {
                    const tilesHtml = match[groupIndex] || '';
                    const tileDivs = tilesHtml.match(/<div[^>]*class=["'][^"']*tile[^"']*["'][^>]*>/g);
                    result.number = tileDivs ? tileDivs.length : 0;

                    // extract color from the first tile class
                    if (tileDivs && tileDivs.length > 0) {
                        const firstTile = tileDivs[0];
                        const colorMatch = firstTile.match(/tile(\d+)/);
                        if (colorMatch) {
                            result.color = parseInt(colorMatch[1], 10);
                        }
                    }

                    groupIndex += 1;
                    break;
                }
                case 'color': {
                    // Color is now extracted from tile classes in the 'number' case
                    // This case is kept for backward compatibility but should not be reached
                    groupIndex += 1;
                    break;
                }
                case 'lineNumber': {
                    // Our lineNumber pattern gave two places: one subcapture with <strong>digits</strong> or a fallback capture for digits.
                    // Try to find a numeric capture in the next few groups.
                    let foundLine: number | undefined;
                    for (let gi = groupIndex; gi < Math.min(match.length, groupIndex + 3); gi++) {
                        const val = match[gi];
                        if (!val) continue;
                        const digitsMatch = val.match(/([0-9]+)/);
                        if (digitsMatch) {
                            foundLine = parseInt(digitsMatch[1], 10);
                            break;
                        }
                    }
                    result.lineNumber = foundLine !== undefined ? foundLine : undefined;
                    groupIndex += 3;
                    break;
                }
                default:
                    groupIndex += 1;
            }
        }

        return result;
    }

    /**
     * Convenience: find matching translation key (if not provided) and parse the input text.
     * Returns parsed object or null if nothing matched.
     */
    static matchAndParse(text: string, key?: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } | null {
        const matchingKey = key || this.findMatchingKey(text);
        if (!matchingKey) {
            return null;
        }

        const regex = this.getRegex(matchingKey);
        // remove HTML comments from the text for matching
        const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');
        const match = cleanText.match(regex);

        if (match) {
            return this.parseMatch(match, matchingKey);
        }

        return null;
    }
}

interface I18nObject {
    nlsStrings: Record<string, Record<string, string>>;
    activeBundle: string;
    jsbundlesversion: string;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {I18nTranslations};
}

window.addEventListener('load', () => {
    const game = new Game();
    if (!game.isSupported()) {
        return;
    }
    game.init();
});
