import { TilePlacement } from './TilePlacement';

export class Bag {
    private tiles: TilePlacement[] = [];

    addTile(tilePlacement: TilePlacement): void {
        const index = (tilePlacement.tileType + 2) % 5;
        if (this.tiles[index] === undefined || this.tiles[index] === null) {
            this.tiles[index] = new TilePlacement(tilePlacement.count, tilePlacement.tileType);
        } else {
            this.tiles[index].count += tilePlacement.count;
        }
        console.log('discard: ' + index + ' ; count: ' + tilePlacement.count);
    }

    removeTile(tilePlacement: TilePlacement): void {
        const index = (tilePlacement.tileType + 2) % 5;
        if (this.tiles[index] && this.tiles[index].count >= tilePlacement.count) {
            console.log('undiscard: ' + index + ' ; count: ' + tilePlacement.count);
            this.tiles[index].count -= tilePlacement.count;
            if (this.tiles[index].count === 0) {
                this.tiles[index] = null;
            }
        }
    }

    getTiles(): TilePlacement[] {
        return this.tiles;
    }

    clear(): void {
        this.tiles = [];
    }
}
