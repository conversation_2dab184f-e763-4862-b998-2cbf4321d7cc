import { TilePlacement } from './TilePlacement';

export class PlayerBoard {
    lines: TilePlacement[] = [];

    constructor() {
        while (this.lines.length < 5) {
            this.lines.push(null);
        }
    }

    addTilesToLine(lineNumber: number, tilePlacement: TilePlacement): TilePlacement | null {
        // lineNumber is 1-based, so index is lineNumber - 1
        const idx = lineNumber - 1;
        const maxTiles = lineNumber;

        const existing = this.lines[idx];

        if (existing && existing.tileType === tilePlacement.tileType) {
            existing.count += tilePlacement.count;
            if (existing.count > maxTiles) {
                const excess = existing.count - maxTiles;
                existing.count = maxTiles;
                return new TilePlacement(excess, tilePlacement.tileType);
            }
            return null;
        } else if (!existing) {
            // No tile yet on this line
            if (tilePlacement.count > maxTiles) {
                this.lines[idx] = new TilePlacement(maxTiles, tilePlacement.tileType);
                return new TilePlacement(tilePlacement.count - maxTiles, tilePlacement.tileType);
            } else {
                this.lines[idx] = new TilePlacement(tilePlacement.count, tilePlacement.tileType);
                return null;
            }
        } else {
            // There is a different tile type already on this line, can't add
            // (You may want to throw or handle this case differently)
            return tilePlacement; // All tiles are excess
        }
    }
}
